# BelBooks/Ledgerly Agent Operations Guide

## CI/CD Overview
- **CI (fast) workflow**: Runs on PRs to `develop`/`main` and manual dispatch. Provisions Postgres (pgvector) and Redis, installs Node 20 + pnpm, sets up Python 3.11 with `uv`, and executes lint (`pnpm lint`), typecheck, build, Vitest unit suites across web/BFF/domain packages, SQL migration application plus invariant tests, Python lint (`ruff`), formatting check (`black --check`), mypy type checking, pytest (with Postgres/Redis URLs), JS/TS and Python coverage collection, and npm audit (non-blocking).
- **SQL lint job**: Separate `sqlfluff` job lints migrations, seeds, and tests; `build-summary` gate surfaces failures from either job.
- **DB migration verification**: `Verify DB Migrations` workflow runs on every PR and on pushes to `main`, booting Supabase locally, applying `packages/db/migrations/all.sql`, optionally running invariants, and shutting the stack down.
- **Staging pipeline**: On pushes to `develop`, performs the full integration run (same setup as fast CI plus JS integration tests via `pnpm test:integration` when present, pytest with services, npm audit). Optional follow-up job `E2E Tests` installs <PERSON>wright if `test:e2e` exists, waits on `vars.STAGING_BASE_URL`, and runs browser tests against staging.
- **Release workflow**: For PRs into `main`, reruns lint/typecheck/test/build (using `pnpm lint:ci`). On pushes to `main`, installs Vercel CLI, fetches prod env config, runs `vercel build` and deploys prebuilt artifacts. Performs staged and production deployment verification loops with curl checks.
- **General CI/CD notes**: All workflows enforce concurrency cancellation per ref, share Node 20/Python 3.11 standards, rely on pnpm store caching, and upload coverage artifacts for audit. Secrets such as `VERCEL_TOKEN` and staging URLs are injected via GitHub environments. Deterministic Supabase type generation happens in CI per `docs/type-management.md` (CI owns canonical `supabase.ts`).

## Local Service Orchestration
- **Primary script**: Run `chmod +x dev-setup.sh` once, then `./dev-setup.sh` to start the full stack, `./dev-setup.sh --check` for health status, and `./dev-setup.sh --stop` to shut everything down. Services launch with dependency ordering, env loading from `.env.local`, and wait-for-health logic.
- **Managed services**: `frontend` (Next.js, :3000), `bff` (API, :4000/health), `workers-py` (FastAPI, :8000/health), `celery-worker` (background processing), `redis` (:6379 ping), `supabase` (:54321 Postgres + APIs). Logs land in `.dev-logs/*.log`; PIDs stored in `.dev-pids/`.
- **Selective control**: Invoke `./dev-setup.sh frontend bff` or any subset to start targeted services. Combine options such as `--timeout=60`, `--verbose`, or `--check --verbose` for diagnostics.
- **Prerequisites**: Script verifies pnpm, Supabase CLI, Docker, optional `uv`, and Redis. macOS installs via npm/brew with Docker Desktop; Linux instructions mirror vendor docs. Script can bootstrap `.env.local` from `.env.example` and validates required variables.
- **Troubleshooting aids**: Tail logs in `.dev-logs`, inspect port usage with `lsof -i :PORT`, kill conflicting PIDs, ensure Supabase/Docker are running, and confirm script execute permissions. Integration aligns with `./bin/setup-local-dev` for first-time bootstrap; after that, agents rely on `dev-setup.sh` for daily work.

## Testing Framework & Approach
- **JavaScript/TypeScript**: Vitest drives unit suites across `apps/web`, `apps/bff`, and domain packages. Primary commands: `pnpm test` (runs each suite serially with verbose output), `pnpm test:watch`, `pnpm test:single`, `pnpm test:coverage` (V8 coverage), and `pnpm test:integration` / `pnpm test:e2e` when present. Type safety enforced via `pnpm typecheck`; linting by `pnpm lint` or CI-strict `pnpm lint:ci` (turbo orchestrated). Husky + lint-staged ensure staged JS files run lint and Prettier.
- **Python workers**: Use `uv` for environment management (`uv venv`, `uv sync --extra dev`). Quality gates include `uv run ruff check`, `uv run black --check`, `uv run mypy`, and `uv run pytest` with coverage configured via `pyproject.toml` (`pytest-cov`, asyncio auto mode). Tests expect Postgres/Redis URLs when integration paths are exercised.
- **Database validation**: SQL invariants live in `packages/db/tests/001_invariants.sql`, executed locally with `psql $DATABASE_URL -f ...` and automatically in CI. Local migrations applied through `pnpm db:migrate:local`; staging deployments via `./bin/apply-staging-migrations`. Supabase types should be regenerated with `pnpm db:types` only when necessary; CI produces the canonical file.
- **Workflow expectations**: Before committing, run `pnpm test`, `pnpm lint`, relevant database checks, and Python lint/tests if touching workers. Follow `docs/llm-coder-guide.md` guidance for type generation and resolving schema-related test failures.
