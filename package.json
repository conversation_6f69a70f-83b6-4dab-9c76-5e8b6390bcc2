{"name": "belbooks-be", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"lint": "turbo run lint", "lint:ci": "turbo run lint -- --max-warnings=0", "lint:changed": "lint-staged", "typecheck": "turbo run typecheck", "build": "turbo run build", "test": "NODE_OPTIONS=--max-old-space-size=3072 vitest run -c apps/web/vitest.config.ts --passWithNoTests --reporter=verbose && NODE_OPTIONS=--max-old-space-size=3072 vitest run -c apps/bff/vitest.config.ts --passWithNoTests --reporter=verbose && NODE_OPTIONS=--max-old-space-size=3072 vitest run --root packages/domain-bank --passWithNoTests --reporter=verbose && NODE_OPTIONS=--max-old-space-size=3072 vitest run --root packages/domain-ledger --passWithNoTests --reporter=verbose && NODE_OPTIONS=--max-old-space-size=3072 vitest run --root packages/domain-vat --passWithNoTests --reporter=verbose && NODE_OPTIONS=--max-old-space-size=3072 vitest run --root packages/types --passWithNoTests --reporter=verbose", "test:watch": "NODE_OPTIONS=--max-old-space-size=3072 vitest", "test:single": "vitest run --reporter=verbose", "test:changed": "vitest --changed", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "dev": "turbo run dev", "db:types": "echo '// Generated from: Local Supabase ($(date))' > packages/types/src/supabase.ts && supabase gen types typescript --local >> packages/types/src/supabase.ts", "db:types:staging": "echo '// Generated from: Staging Supabase ($(date))' > packages/types/src/supabase.ts && supabase gen types typescript --linked >> packages/types/src/supabase.ts", "db:migrate:staging": "./bin/apply-staging-migrations", "db:migrate:local": "psql $DATABASE_URL -f packages/db/migrations/all.sql", "db:seed:local": "psql $DATABASE_URL -f packages/db/seeds/dev_seed.sql", "reset-docs": "./scripts/reset-documents.sh", "reset-docs:staging": "./scripts/reset-documents.sh staging", "reset-docs:prod": "./scripts/reset-documents.sh prod", "prepare": "husky install"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["bash -c 'pnpm lint:ci'", "npx prettier --write"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitest/coverage-v8": "^3.2.4", "eslint": "^8.57.1", "eslint-plugin-security": "^3.0.1", "eslint-plugin-vitest": "^0.5.4", "husky": "^8.0.0", "lint-staged": "^16.1.6", "turbo": "^2.0.0", "vitest": "^3.2.4"}, "engines": {"node": ">=20.0.0", "pnpm": "9.0.0"}, "pnpm": {"overrides": {"typescript": "5.5.4"}}, "dependencies": {"@supabase/ssr": "^0.7.0", "@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3"}}