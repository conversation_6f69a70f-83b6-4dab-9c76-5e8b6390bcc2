import { useState, useEffect, useCallback } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import { useAuth } from '@/contexts/AuthContext'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { rpcVatPreview } from '@belbooks/dal'
import type { VATSummary } from '@belbooks/types'

export interface VATPosition {
  amount: number
  currency: string
  dueDate: string | null
  type: 'due' | 'refund'
  period: {
    start: string
    end: string
  }
}

export interface VATSummaryHook {
  vatPosition: VATPosition | null
  loading: boolean
  error: string | null
  refreshVAT: () => Promise<void>
}

// Helper to get current VAT period (quarterly for Belgium)
function getCurrentVATQuarter(): { start: string; end: string } {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() // 0-based

  let quarterStart: Date
  let quarterEnd: Date

  if (month < 3) {
    // Q1: Jan-Mar
    quarterStart = new Date(year, 0, 1) // Jan 1
    quarterEnd = new Date(year, 2, 31) // Mar 31
  } else if (month < 6) {
    // Q2: Apr-Jun
    quarterStart = new Date(year, 3, 1) // Apr 1
    quarterEnd = new Date(year, 5, 30) // Jun 30
  } else if (month < 9) {
    // Q3: Jul-Sep
    quarterStart = new Date(year, 6, 1) // Jul 1
    quarterEnd = new Date(year, 8, 30) // Sep 30
  } else {
    // Q4: Oct-Dec
    quarterStart = new Date(year, 9, 1) // Oct 1
    quarterEnd = new Date(year, 11, 31) // Dec 31
  }

  return {
    start: quarterStart.toISOString().split('T')[0], // YYYY-MM-DD
    end: quarterEnd.toISOString().split('T')[0], // YYYY-MM-DD
  }
}

// Helper to calculate VAT due date (typically 20th of month following quarter end)
function getVATDueDate(quarterEnd: string): string {
  const endDate = new Date(quarterEnd)
  const dueDate = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 20)
  return dueDate.toISOString().split('T')[0]
}

export const useVATSummary = (): VATSummaryHook => {
  const { user } = useAuth()
  const { currentEntity, isValid } = useOrgEntitySelection()
  const [vatPosition, setVATPosition] = useState<VATPosition | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchVATSummary = useCallback(async () => {
    if (!user || !isValid || !currentEntity) {
      setVATPosition(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const supabase = createClientSupabaseClient()
      const entityId = currentEntity.entity_id
      const period = getCurrentVATQuarter()

      // Ensure entityId is valid
      if (!entityId) {
        setVATPosition(null)
        setLoading(false)
        return
      }

      // Use the existing VAT preview RPC function
      const vatPreviewData = await rpcVatPreview(supabase, {
        p_entity: entityId,
        p_start: period.start,
        p_end: period.end,
      })

      const { summary } = vatPreviewData
      const netVATPayable = summary.net_vat_payable
      const dueDate = getVATDueDate(period.end)

      setVATPosition({
        amount: Math.abs(netVATPayable),
        currency: 'EUR',
        dueDate,
        type: netVATPayable >= 0 ? 'due' : 'refund',
        period,
      })
    } catch (err) {
      // Handle VAT disabled or other errors gracefully
      if (err instanceof Error && err.message.includes('VAT')) {
        setVATPosition(null) // VAT not enabled for this entity
      } else {
        setError(
          err instanceof Error ? err.message : 'Failed to load VAT summary'
        )
        console.error('VAT summary error:', err)
      }
    } finally {
      setLoading(false)
    }
  }, [user, isValid, currentEntity])

  useEffect(() => {
    void fetchVATSummary()
  }, [fetchVATSummary])

  const refreshVAT = async () => {
    await fetchVATSummary()
  }

  return {
    vatPosition,
    loading,
    error,
    refreshVAT,
  }
}
