import { NextResponse } from 'next/server'
import { createSecureServerClient } from '@/lib/supabase-server'
import { sessionManager } from '@/lib/session-security'
import { extractRequestInfo } from '@/lib/security-monitoring'

export async function POST(request: Request) {
  try {
    const supabase = createSecureServerClient()
    const requestData = (await request.json()) as {
      event: string
      session: unknown
    }
    const { event, session } = requestData

    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
      // Set/update the session cookies for SSR (middleware)
      await supabase.auth.setSession(session as never)

      // Initialize/update session security manager for this session
      try {
        const s = session as any
        const token: string | undefined = s?.access_token
        const userId: string | undefined = s?.user?.id
        if (token && userId) {
          const existing = sessionManager.getSession(token)
          if (!existing) {
            await sessionManager.initializeSession(token, userId, {
              headers: request.headers,
            } as any)
          } else {
            sessionManager.updateActivity(token, {
              headers: request.headers,
            } as any)
          }
        }
      } catch (e) {
        console.warn('Auth events: session manager init failed', e)
      }
    }

    if (event === 'SIGNED_OUT') {
      await supabase.auth.signOut()
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Auth callback error:', error)
    return NextResponse.json(
      { success: false, error: 'callback_failed' },
      { status: 400 }
    )
  }
}

export function OPTIONS() {
  return NextResponse.json({})
}
