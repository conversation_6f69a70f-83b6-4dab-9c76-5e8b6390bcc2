"""Stub adapter for testing and development."""

import hashlib
import logging
from typing import Any, Dict, <PERSON><PERSON>

from .base import ExtractionAdapter
from ..models import ExtractionResult, InvoiceInfo, LineItem, SupplierInfo, VatRate

logger = logging.getLogger(__name__)


class StubExtractionAdapter(ExtractionAdapter):
    """Deterministic stub adapter for testing and development."""

    async def extract(self, text: str) -> Tuple[ExtractionResult, float, Dict[str, Any]]:
        """
        Generate deterministic extraction based on text hash.
        
        This ensures consistent results for testing while providing realistic data.
        """
        logger.info("Processing with stub adapter")

        # Generate deterministic data based on text hash
        text_hash = hashlib.sha256(text.encode('utf-8')).hexdigest()[:8]

        # Use hash to determine supplier and amounts
        hash_int = int(text_hash, 16)
        supplier_idx = hash_int % 3
        amount_base = (hash_int % 1000) + 100  # 100-1099 range

        suppliers = [
            {"name": "ACME Office Supplies", "vat": "BE0123456789", "iban": "BE12 3456 7890 1234"},
            {"name": "Green Energy Solutions", "vat": "BE0987654321", "iban": "BE98 7654 3210 9876"},
            {"name": "Premium Consulting Services", "vat": "BE0555123456", "iban": "BE55 5123 4567 8901"}
        ]

        supplier_data = suppliers[supplier_idx]
        net_amount = f"{amount_base}.00"
        vat_amount = f"{amount_base * 0.21:.2f}"
        gross_amount = f"{amount_base * 1.21:.2f}"

        extraction_result = ExtractionResult(
            supplier=SupplierInfo(
                name=supplier_data["name"],
                vat=supplier_data["vat"],
                iban=supplier_data["iban"]
            ),
            invoice=InvoiceInfo(
                number=f"INV-2024-{hash_int % 1000:03d}",
                issue_date="2024-08-26",
                due_date="2024-09-26",
                currency="EUR",
                net=net_amount,
                vat=vat_amount,
                gross=gross_amount
            ),
            lines=[
                LineItem(
                    description=f"Professional services - {hash_int % 100}",
                    quantity="1",
                    unit_price=net_amount,
                    vat_rate=VatRate.STANDARD
                )
            ]
        )

        # Deterministic confidence based on hash
        confidence = 0.85 + (hash_int % 15) / 100  # 0.85-0.99 range

        metadata = {
            "provider": "stub",
            "text_hash": text_hash,
            "text_length": len(text),
            "deterministic": True
        }

        return extraction_result, confidence, metadata