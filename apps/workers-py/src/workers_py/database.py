"""Database connection and management using asyncpg."""

import logging
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from typing import Any

import asyncpg
from asyncpg import Connection, Pool

from .config import settings

logger = logging.getLogger(__name__)

# Global connection pool
_pool: Pool | None = None


async def init_database() -> None:
    """Initialize database connection pool."""
    global _pool

    if _pool is not None:
        logger.warning("Database pool already initialized")
        return

    try:
        logger.info("Initializing database connection pool")

        _pool = await asyncpg.create_pool(
            dsn=settings.DATABASE_URL,
            min_size=1,
            max_size=settings.DATABASE_POOL_SIZE,
            max_inactive_connection_lifetime=300,  # 5 minutes
            command_timeout=60,
            server_settings={
                "application_name": "workers-py",
                "timezone": "UTC",
            },
        )

        # Test the connection
        async with _pool.acquire() as conn:
            await conn.execute("SELECT 1")

        logger.info("Database connection pool initialized successfully")

        # Create tables if they don't exist
        await create_tables()

    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def close_database() -> None:
    """Close database connection pool."""
    global _pool

    if _pool is not None:
        logger.info("Closing database connection pool")
        await _pool.close()
        _pool = None


async def ensure_database_initialized() -> None:
    """Ensure database is initialized, initialize if needed."""
    global _pool
    if _pool is None:
        logger.info("Database not initialized in worker process, initializing now...")
        await init_database()


async def get_database() -> AsyncGenerator[Connection, None]:
    """Get a database connection from the pool."""
    # Ensure database is initialized (important for worker processes)
    await ensure_database_initialized()

    if _pool is None:
        raise RuntimeError("Database pool not initialized. Call init_database() first.")

    async with _pool.acquire() as conn:
        try:
            yield conn
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            raise


from contextlib import asynccontextmanager

@asynccontextmanager
async def get_database_connection():
    """Get a database connection as an async context manager."""
    async for conn in get_database():
        yield conn
        break


async def create_tables() -> None:
    """Create necessary database tables."""
    tables_sql = """
    -- Document processing results table
    CREATE TABLE IF NOT EXISTS document_processing_results (
        id SERIAL PRIMARY KEY,
        entity_id VARCHAR(255) NOT NULL,
        file_url TEXT NOT NULL,
        task_id VARCHAR(255) UNIQUE NOT NULL,
        status VARCHAR(50) NOT NULL DEFAULT 'pending',
        extracted_text TEXT,
        metadata JSONB DEFAULT '{}',
        ai_analysis JSONB DEFAULT '{}',
        error_message TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

        -- Indexes
        UNIQUE(entity_id, file_url)
    );

    -- Index for faster queries
    CREATE INDEX IF NOT EXISTS idx_document_processing_entity_id
        ON document_processing_results(entity_id);
    CREATE INDEX IF NOT EXISTS idx_document_processing_task_id
        ON document_processing_results(task_id);
    CREATE INDEX IF NOT EXISTS idx_document_processing_status
        ON document_processing_results(status);
    CREATE INDEX IF NOT EXISTS idx_document_processing_created_at
        ON document_processing_results(created_at);

    -- Add GIN index for JSONB columns for better search performance
    CREATE INDEX IF NOT EXISTS idx_document_processing_metadata
        ON document_processing_results USING GIN (metadata);
    CREATE INDEX IF NOT EXISTS idx_document_processing_ai_analysis
        ON document_processing_results USING GIN (ai_analysis);

    -- Task status tracking table (optional, for detailed task tracking)
    CREATE TABLE IF NOT EXISTS task_status (
        task_id VARCHAR(255) PRIMARY KEY,
        status VARCHAR(50) NOT NULL DEFAULT 'pending',
        progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
        stage VARCHAR(100),
        message TEXT,
        result JSONB,
        error_message TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Index for task status queries
    CREATE INDEX IF NOT EXISTS idx_task_status_status
        ON task_status(status);
    CREATE INDEX IF NOT EXISTS idx_task_status_created_at
        ON task_status(created_at);

    -- File processing cache table (to avoid reprocessing same files)
    CREATE TABLE IF NOT EXISTS file_processing_cache (
        id SERIAL PRIMARY KEY,
        file_url TEXT NOT NULL,
        file_hash VARCHAR(64), -- SHA-256 hash of file content
        file_size BIGINT,
        processing_options_hash VARCHAR(64), -- Hash of processing options
        extracted_text TEXT,
        metadata JSONB DEFAULT '{}',
        ai_analysis JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

        -- Unique constraint on file_url and options hash
        UNIQUE(file_url, processing_options_hash)
    );

    -- Index for cache lookups
    CREATE INDEX IF NOT EXISTS idx_file_cache_url_options
        ON file_processing_cache(file_url, processing_options_hash);
    CREATE INDEX IF NOT EXISTS idx_file_cache_hash
        ON file_processing_cache(file_hash);

    -- Update trigger for updated_at timestamps
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';

    -- Apply update triggers
    DROP TRIGGER IF EXISTS update_document_processing_results_updated_at ON document_processing_results;
    CREATE TRIGGER update_document_processing_results_updated_at
        BEFORE UPDATE ON document_processing_results
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

    DROP TRIGGER IF EXISTS update_task_status_updated_at ON task_status;
    CREATE TRIGGER update_task_status_updated_at
        BEFORE UPDATE ON task_status
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    """

    try:
        if _pool is None:
            raise RuntimeError("Database pool not initialized")

        async with _pool.acquire() as conn:
            await conn.execute(tables_sql)
        logger.info("Database tables created/verified successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


class DatabaseManager:
    """Database operations manager."""

    @staticmethod
    async def save_processing_result(
        entity_id: str,
        file_url: str,
        task_id: str,
        status: str,
        extracted_text: str | None = None,
        metadata: dict[str, Any] | None = None,
        ai_analysis: dict[str, Any] | None = None,
        error_message: str | None = None,
    ) -> None:
        """Save or update document processing result."""
        async with get_database() as conn:
            await conn.execute(
                """
                INSERT INTO document_processing_results
                (entity_id, file_url, task_id, status, extracted_text, metadata, ai_analysis, error_message)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT (entity_id, file_url) DO UPDATE SET
                    task_id = EXCLUDED.task_id,
                    status = EXCLUDED.status,
                    extracted_text = EXCLUDED.extracted_text,
                    metadata = EXCLUDED.metadata,
                    ai_analysis = EXCLUDED.ai_analysis,
                    error_message = EXCLUDED.error_message,
                    updated_at = NOW()
                """,
                entity_id,
                file_url,
                task_id,
                status,
                extracted_text,
                metadata or {},
                ai_analysis or {},
                error_message,
            )

    @staticmethod
    async def get_processing_result(
        entity_id: str, file_url: str
    ) -> dict[str, Any] | None:
        """Get processing result by entity_id and file_url."""
        async with get_database() as conn:
            row = await conn.fetchrow(
                """
                SELECT * FROM document_processing_results
                WHERE entity_id = $1 AND file_url = $2
                """,
                entity_id,
                file_url,
            )
            return dict(row) if row else None

    @staticmethod
    async def get_processing_results_by_entity(entity_id: str) -> list[dict[str, Any]]:
        """Get all processing results for an entity."""
        async with get_database() as conn:
            rows = await conn.fetch(
                """
                SELECT * FROM document_processing_results
                WHERE entity_id = $1
                ORDER BY created_at DESC
                """,
                entity_id,
            )
            return [dict(row) for row in rows]

    @staticmethod
    async def update_task_status(
        task_id: str,
        status: str,
        progress: int | None = None,
        stage: str | None = None,
        message: str | None = None,
        result: dict[str, Any] | None = None,
        error_message: str | None = None,
    ) -> None:
        """Update task status."""
        async with get_database() as conn:
            await conn.execute(
                """
                INSERT INTO task_status
                (task_id, status, progress, stage, message, result, error_message)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (task_id) DO UPDATE SET
                    status = EXCLUDED.status,
                    progress = EXCLUDED.progress,
                    stage = EXCLUDED.stage,
                    message = EXCLUDED.message,
                    result = EXCLUDED.result,
                    error_message = EXCLUDED.error_message,
                    updated_at = NOW()
                """,
                task_id,
                status,
                progress,
                stage,
                message,
                result,
                error_message,
            )

    @staticmethod
    async def get_task_status(task_id: str) -> dict[str, Any] | None:
        """Get task status by task_id."""
        async with get_database() as conn:
            row = await conn.fetchrow(
                "SELECT * FROM task_status WHERE task_id = $1", task_id
            )
            return dict(row) if row else None

    @staticmethod
    async def cleanup_old_tasks(days: int = 30) -> int:
        """Clean up old completed/failed tasks."""
        async with get_database() as conn:
            result = await conn.execute(
                """
                DELETE FROM task_status
                WHERE status IN ('completed', 'failed')
                AND created_at < NOW() - ($1::int || ' days')::interval
                """,
                days,
            )

            # Also clean up old processing results
            await conn.execute(
                """
                DELETE FROM document_processing_results
                WHERE status IN ('completed', 'failed')
                AND created_at < NOW() - ($1::int || ' days')::interval
                """,
                days,
            )

            return int(result.split()[-1])  # Extract number of affected rows


# Utility functions for raw SQL operations
async def execute_query(query: str, *args) -> Any:
    """Execute a query and return the result."""
    async with get_database() as conn:
        return await conn.execute(query, *args)


async def fetch_one(query: str, *args) -> dict[str, Any] | None:
    """Fetch one row from a query."""
    async with get_database() as conn:
        row = await conn.fetchrow(query, *args)
        return dict(row) if row else None


async def fetch_all(query: str, *args) -> list[dict[str, Any]]:
    """Fetch all rows from a query."""
    async with get_database() as conn:
        rows = await conn.fetch(query, *args)
        return [dict(row) for row in rows]
