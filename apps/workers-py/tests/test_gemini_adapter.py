"""Tests for GeminiLangExtractAdapter."""

import pytest
from unittest.mock import Mock, patch, AsyncMock

from workers_py.adapters.gemini_langextract import GeminiLangExtractAdapter
from workers_py.adapters.base import ExtractionError
from workers_py.models import ExtractionResult, VatRate


class TestGeminiLangExtractAdapter:
    """Test suite for GeminiLangExtractAdapter."""

    @pytest.fixture
    def adapter(self):
        """Create adapter instance."""
        return GeminiLangExtractAdapter(model_id="gemini-2.5-flash")

    @pytest.fixture
    def sample_invoice_text(self):
        """Sample invoice text for testing."""
        return """
        INVOICE 2024-INV-123
        Issue date: 2024-08-15
        Due: 2024-09-15
        
        Supplier: Test Company BVBA
        VAT: BE0123456789
        IBAN: BE68 5390 0754 7034
        
        1 x Consulting services @ 100.00 EUR
        VAT 21%: 21.00
        TOTAL: 121.00 EUR
        """

    @pytest.fixture
    def mock_langextract_result(self):
        """Mock LangExtract result."""
        mock_extraction = Mock()
        mock_extraction.extraction_class = "supplier"
        mock_extraction.extraction_text = "Test Company BVBA"
        mock_extraction.attributes = {
            "name": "Test Company BVBA",
            "vat": "BE0123456789",
            "iban": "BE68 5390 0754 7034"
        }
        mock_extraction.start_char = 50
        
        mock_invoice = Mock()
        mock_invoice.extraction_class = "invoice_header"
        mock_invoice.extraction_text = "INVOICE 2024-INV-123"
        mock_invoice.attributes = {
            "number": "2024-INV-123",
            "issueDate": "2024-08-15",
            "dueDate": "2024-09-15",
            "currency": "EUR",
            "net": "100.00",
            "vat": "21.00",
            "gross": "121.00"
        }
        mock_invoice.start_char = 0

        mock_line = Mock()
        mock_line.extraction_class = "line_item"
        mock_line.extraction_text = "1 x Consulting services @ 100.00 EUR"
        mock_line.attributes = {
            "description": "Consulting services",
            "quantity": "1",
            "unitPrice": "100.00",
            "vatRate": 21
        }
        mock_line.start_char = 120

        mock_result = Mock()
        mock_result.extractions = [mock_extraction, mock_invoice, mock_line]
        
        return mock_result

    @patch('workers_py.adapters.gemini_langextract.lx.extract')
    async def test_successful_extraction(self, mock_extract, adapter, sample_invoice_text, mock_langextract_result):
        """Test successful extraction with valid invoice text."""
        mock_extract.return_value = mock_langextract_result
        
        result, confidence, metadata = await adapter.extract(sample_invoice_text)
        
        assert isinstance(result, ExtractionResult)
        assert result.supplier.name == "Test Company BVBA"
        assert result.supplier.vat == "BE0123456789"
        assert result.invoice.number == "2024-INV-123"
        assert result.invoice.net == "100.00"
        assert result.invoice.vat == "21.00"
        assert result.invoice.gross == "121.00"
        assert len(result.lines) == 1
        assert result.lines[0].vat_rate == VatRate.STANDARD
        
        assert 0.0 <= confidence <= 1.0
        assert metadata["provider"] == "gemini_langextract"
        assert metadata["model"] == "gemini-2.5-flash"

    async def test_empty_text_raises_error(self, adapter):
        """Test that empty text raises ExtractionError."""
        with pytest.raises(ExtractionError, match="Empty text provided"):
            await adapter.extract("")

    @patch('workers_py.adapters.gemini_langextract.lx.extract')
    async def test_langextract_failure(self, mock_extract, adapter, sample_invoice_text):
        """Test handling of LangExtract failures."""
        mock_extract.side_effect = Exception("API error")
        
        with pytest.raises(ExtractionError, match="Gemini extraction failed"):
            await adapter.extract(sample_invoice_text)

    def test_clean_vat_number(self, adapter):
        """Test VAT number cleaning and validation."""
        # Valid Belgian VAT
        assert adapter._clean_vat_number("BE0123456789") == "BE0123456789"
        assert adapter._clean_vat_number("BE 0123 456 789") == "BE0123456789"
        
        # Invalid format - return original
        assert adapter._clean_vat_number("DE123456789") == "DE123456789"
        assert adapter._clean_vat_number("INVALID") == "INVALID"
        
        # None/empty
        assert adapter._clean_vat_number(None) is None
        assert adapter._clean_vat_number("") is None

    def test_clean_iban(self, adapter):
        """Test IBAN cleaning and formatting."""
        # Valid Belgian IBAN
        assert adapter._clean_iban("****************") == "BE68 5390 0754 7034"
        assert adapter._clean_iban("BE68 5390 0754 7034") == "BE68 5390 0754 7034"
        
        # Invalid format - return original
        assert adapter._clean_iban("INVALID") == "INVALID"
        
        # None/empty
        assert adapter._clean_iban(None) is None

    def test_normalize_date(self, adapter):
        """Test date normalization."""
        # Already in correct format
        assert adapter._normalize_date("2024-08-15") == "2024-08-15"
        
        # European format
        assert adapter._normalize_date("15-08-2024") == "2024-08-15"
        assert adapter._normalize_date("15/08/2024") == "2024-08-15"
        assert adapter._normalize_date("5/8/2024") == "2024-08-05"
        
        # Invalid format
        assert adapter._normalize_date("invalid") is None
        assert adapter._normalize_date("") is None

    def test_normalize_amount(self, adapter):
        """Test amount normalization."""
        # Valid amounts
        assert adapter._normalize_amount("100.00") == "100.00"
        assert adapter._normalize_amount("100,50") == "100.50"
        assert adapter._normalize_amount("€ 100.50") == "100.50"
        assert adapter._normalize_amount("100.5") == "100.5"

        # Invalid amounts
        assert adapter._normalize_amount("invalid") is None
        assert adapter._normalize_amount("") is None

    def test_normalize_vat_rate(self, adapter):
        """Test VAT rate normalization across input formats."""
        assert adapter._normalize_vat_rate(21) == VatRate.STANDARD
        assert adapter._normalize_vat_rate("21%") == VatRate.STANDARD
        assert adapter._normalize_vat_rate("12.0") == VatRate.REDUCED
        assert adapter._normalize_vat_rate("6,0") == VatRate.LOW
        assert adapter._normalize_vat_rate("not-a-rate") is None
        assert adapter._normalize_vat_rate(None) is None

    @patch('workers_py.adapters.gemini_langextract.lx.extract')
    async def test_confidence_calculation(self, mock_extract, adapter, sample_invoice_text):
        """Test confidence score calculation."""
        # Create minimal extraction for confidence testing
        mock_extraction = Mock()
        mock_extraction.extraction_class = "supplier"
        mock_extraction.extraction_text = "Test Company"
        mock_extraction.attributes = {"name": "Test Company"}
        mock_extraction.start_char = 0  # Grounded

        mock_invoice = Mock()
        mock_invoice.extraction_class = "invoice_header"
        mock_invoice.extraction_text = "INVOICE 123"
        mock_invoice.attributes = {
            "number": "123",
            "issueDate": "2024-08-15",
            "net": "100.00",
            "vat": "21.00",
            "gross": "121.00"
        }
        mock_invoice.start_char = 20

        mock_result = Mock()
        mock_result.extractions = [mock_extraction, mock_invoice]
        mock_extract.return_value = mock_result

        result, confidence, metadata = await adapter.extract(sample_invoice_text)
        
        # Should have reasonable confidence due to grounding and arithmetic consistency
        assert confidence > 0.5
        assert "grounded_count" in metadata
        assert metadata["grounded_count"] == 2

    @patch('workers_py.adapters.gemini_langextract.lx.extract')
    async def test_fallback_line_creation(self, mock_extract, adapter, sample_invoice_text):
        """Test fallback line creation when no lines are extracted."""
        # Mock extraction with no line items
        mock_supplier = Mock()
        mock_supplier.extraction_class = "supplier"
        mock_supplier.extraction_text = "Test Company"
        mock_supplier.attributes = {"name": "Test Company"}

        mock_invoice = Mock()
        mock_invoice.extraction_class = "invoice_header" 
        mock_invoice.extraction_text = "INVOICE 123"
        mock_invoice.attributes = {
            "number": "123",
            "net": "100.00",
            "vat": "21.00",
            "gross": "121.00"
        }

        mock_result = Mock()
        mock_result.extractions = [mock_supplier, mock_invoice]
        mock_extract.return_value = mock_result

        result, confidence, metadata = await adapter.extract(sample_invoice_text)
        
        # Should create fallback line
        assert len(result.lines) == 1
        assert result.lines[0].description == "Invoice total"
        assert result.lines[0].unit_price == "100.00"
        assert result.lines[0].vat_rate == VatRate.STANDARD

    def test_get_field_coverage(self, adapter):
        """Test field coverage calculation."""
        # Create minimal extraction result
        from workers_py.models import ExtractionResult, SupplierInfo, InvoiceInfo, LineItem
        
        extraction = ExtractionResult(
            supplier=SupplierInfo(name="Test Company", vat="BE0123456789"),
            invoice=InvoiceInfo(
                number="123",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00", 
                gross="121.00"
            ),
            lines=[
                LineItem(
                    description="Test item",
                    quantity="1",
                    unit_price="100.00",
                    vat_rate=VatRate.STANDARD
                )
            ]
        )
        
        coverage = adapter._get_field_coverage(extraction)
        
        assert coverage["supplier_name"] is True
        assert coverage["supplier_vat"] is True
        assert coverage["invoice_number"] is True
        assert coverage["invoice_date"] is True
        assert coverage["invoice_amounts"] is True
        assert coverage["has_lines"] is True
        assert coverage["line_count"] == 1
