"""Tests for TextExtractionService."""

import pytest
from unittest.mock import Mock, patch, MagicMock

from workers_py.services.text_extraction import TextExtractionService, TextExtractionError


class TestTextExtractionService:
    """Test suite for TextExtractionService."""

    @pytest.fixture
    def service(self):
        """Create service instance."""
        return TextExtractionService()

    def test_extract_text_empty_bytes(self, service):
        """Test extraction with empty bytes raises error."""
        with pytest.raises(TextExtractionError, match="Empty file bytes provided"):
            service.extract_text(b"")

    @patch('workers_py.services.text_extraction.magic.from_buffer')
    def test_extract_text_pdf_success(self, mock_magic, service):
        """Test successful PDF text extraction."""
        mock_magic.return_value = "application/pdf"
        
        with patch.object(service, '_extract_pdf_text', return_value="Extracted PDF text") as mock_pdf:
            result = service.extract_text(b"fake pdf bytes")
            
            assert result == "Extracted PDF text"
            mock_pdf.assert_called_once_with(b"fake pdf bytes")

    @patch('workers_py.services.text_extraction.magic.from_buffer')
    def test_extract_text_image_success(self, mock_magic, service):
        """Test successful image text extraction."""
        mock_magic.return_value = "image/jpeg"
        
        with patch.object(service, '_extract_image_text', return_value="Extracted image text") as mock_image:
            result = service.extract_text(b"fake image bytes")
            
            assert result == "Extracted image text"
            mock_image.assert_called_once_with(b"fake image bytes")

    @patch('workers_py.services.text_extraction.magic.from_buffer')
    def test_extract_text_unsupported_mime(self, mock_magic, service):
        """Test extraction with unsupported MIME type."""
        mock_magic.return_value = "application/zip"
        
        with pytest.raises(TextExtractionError, match="Unsupported MIME type"):
            service.extract_text(b"fake zip bytes")

    @patch('workers_py.services.text_extraction.magic.from_buffer')
    def test_extract_text_no_text_extracted(self, mock_magic, service):
        """Test when no text is extracted."""
        mock_magic.return_value = "application/pdf"
        
        with patch.object(service, '_extract_pdf_text', return_value="") as mock_pdf:
            with pytest.raises(TextExtractionError, match="No text extracted"):
                service.extract_text(b"fake pdf bytes")

    @patch('pdfminer.high_level.extract_text')
    def test_extract_pdf_text_success(self, mock_extract, service):
        """Test successful PDF text extraction."""
        mock_extract.return_value = "Sample PDF text content"
        
        result = service._extract_pdf_text(b"fake pdf bytes")
        
        assert result == "Sample PDF text content"

    @patch('pdfminer.high_level.extract_text')
    def test_extract_pdf_text_failure_fallback_to_ocr(self, mock_extract, service):
        """Test PDF extraction failure falls back to OCR."""
        mock_extract.side_effect = Exception("PDF parsing failed")
        
        with patch.object(service, '_extract_image_text', return_value="OCR fallback text") as mock_ocr:
            result = service._extract_pdf_text(b"fake pdf bytes")
            
            assert result == "OCR fallback text"
            mock_ocr.assert_called_once_with(b"fake pdf bytes")

    @patch('cv2.imdecode')
    @patch('pytesseract.image_to_string')
    def test_extract_image_text_success(self, mock_tesseract, mock_imdecode, service):
        """Test successful image text extraction."""
        # Mock OpenCV image decoding
        mock_image = MagicMock()
        mock_imdecode.return_value = mock_image
        
        # Mock Tesseract OCR
        mock_tesseract.return_value = "Extracted text from image"
        
        result = service._extract_image_text(b"fake image bytes")
        
        assert result == "Extracted text from image"
        mock_tesseract.assert_called_once_with(mock_image, config='--psm 6')

    @patch('cv2.imdecode')
    def test_extract_image_text_opencv_failure(self, mock_imdecode, service):
        """Test image extraction with OpenCV failure."""
        mock_imdecode.side_effect = ImportError("OpenCV not available")
        
        with pytest.raises(TextExtractionError, match="OCR dependencies not available"):
            service._extract_image_text(b"fake image bytes")

    @patch('cv2.imdecode')
    @patch('pytesseract.image_to_string')
    def test_extract_image_text_tesseract_failure(self, mock_tesseract, mock_imdecode, service):
        """Test image extraction with Tesseract failure."""
        mock_image = MagicMock()
        mock_imdecode.return_value = mock_image
        mock_tesseract.side_effect = Exception("Tesseract failed")
        
        with pytest.raises(TextExtractionError, match="OCR extraction failed"):
            service._extract_image_text(b"fake image bytes")

    def test_is_pdf_mime_type(self, service):
        """Test PDF MIME type detection."""
        assert service._is_pdf_mime_type("application/pdf") is True
        assert service._is_pdf_mime_type("application/x-pdf") is True
        assert service._is_pdf_mime_type("image/jpeg") is False
        assert service._is_pdf_mime_type("text/plain") is False

    def test_is_image_mime_type(self, service):
        """Test image MIME type detection."""
        assert service._is_image_mime_type("image/jpeg") is True
        assert service._is_image_mime_type("image/png") is True
        assert service._is_image_mime_type("image/tiff") is True
        assert service._is_image_mime_type("image/bmp") is True
        assert service._is_image_mime_type("application/pdf") is False
        assert service._is_image_mime_type("text/plain") is False

    @patch('workers_py.services.text_extraction.magic.from_buffer')
    def test_extract_text_with_whitespace_only(self, mock_magic, service):
        """Test extraction that returns only whitespace."""
        mock_magic.return_value = "application/pdf"
        
        with patch.object(service, '_extract_pdf_text', return_value="   \n\t  ") as mock_pdf:
            with pytest.raises(TextExtractionError, match="No text extracted"):
                service.extract_text(b"fake pdf bytes")

    @patch('workers_py.services.text_extraction.magic.from_buffer')
    def test_extract_text_magic_failure(self, mock_magic, service):
        """Test extraction when magic library fails."""
        mock_magic.side_effect = Exception("Magic library error")
        
        with pytest.raises(TextExtractionError, match="MIME type detection failed"):
            service.extract_text(b"fake bytes")

    def test_extract_text_integration_pdf_to_image_fallback(self, service):
        """Test integration: PDF extraction fails, falls back to image OCR."""
        with patch('workers_py.services.text_extraction.magic.from_buffer', return_value="application/pdf"):
            with patch.object(service, '_extract_pdf_text', side_effect=Exception("PDF failed")):
                with patch.object(service, '_extract_image_text', return_value="OCR success"):
                    result = service.extract_text(b"fake pdf bytes")
                    assert result == "OCR success"

    def test_extract_text_integration_image_direct(self, service):
        """Test integration: Direct image extraction."""
        with patch('workers_py.services.text_extraction.magic.from_buffer', return_value="image/png"):
            with patch.object(service, '_extract_image_text', return_value="Direct OCR"):
                result = service.extract_text(b"fake image bytes")
                assert result == "Direct OCR"
