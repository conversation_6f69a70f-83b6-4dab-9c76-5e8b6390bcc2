"""Tests for EnhancedExtractionService."""

import pytest
from unittest.mock import Mock, patch, AsyncMock

from workers_py.services.extract_v2 import EnhancedExtractionService, ExtractedDocument
from workers_py.adapters.base import ExtractionError
from workers_py.models import ExtractionResult, SupplierInfo, InvoiceInfo


class TestEnhancedExtractionService:
    """Test suite for EnhancedExtractionService."""

    @pytest.fixture
    def service(self):
        """Create service instance."""
        return EnhancedExtractionService(entity_id=123)

    @pytest.fixture
    def sample_text(self):
        """Sample invoice text for testing."""
        return """
        INVOICE 2024-INV-123
        Issue date: 2024-08-15
        
        Supplier: Test Company BVBA
        VAT: BE0123456789
        
        1 x Consulting services @ 100.00 EUR
        VAT 21%: 21.00
        TOTAL: 121.00 EUR
        """



    async def test_process_document_success(self, service, sample_text):
        """Test successful document processing."""
        # Create mock result inline
        mock_result = ExtractionResult(
            supplier=SupplierInfo(name="Test Company BVBA", vat="BE0123456789"),
            invoice=InvoiceInfo(
                number="2024-INV-123",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00"
            ),
            lines=[]
        )

        # Mock the primary adapter
        service.primary_adapter.extract = AsyncMock(
            return_value=(mock_result, 0.85, {"provider": "gemini_langextract"})
        )

        result = await service.process_document(sample_text, entity_id=123)

        assert isinstance(result, ExtractedDocument)
        assert result.primary_result == mock_result
        assert result.primary_confidence == 0.85
        assert result.primary_metadata["provider"] == "gemini_langextract"
        assert result.text_length == len(sample_text)
        assert result.processing_time > 0

    async def test_process_document_empty_text(self, service):
        """Test processing with empty text raises error."""
        with pytest.raises(ExtractionError, match="Empty text provided"):
            await service.process_document("", entity_id=123)

    async def test_process_document_with_shadow_mode(self, service, sample_text, mock_extraction_result):
        """Test document processing with shadow mode."""
        # Set up shadow mode
        service.run_mode = "shadow"
        service.shadow_adapter = Mock()
        service.shadow_adapter.extract = AsyncMock(
            return_value=(mock_extraction_result, 0.75, {"provider": "stub"})
        )
        service.primary_adapter.extract = AsyncMock(
            return_value=(mock_extraction_result, 0.85, {"provider": "gemini_langextract"})
        )
        
        result = await service.process_document(sample_text, entity_id=123)
        
        assert result.shadow_result == mock_extraction_result
        assert result.shadow_confidence == 0.75
        assert result.shadow_metadata["provider"] == "stub"

    async def test_process_document_with_retry(self, service, sample_text, mock_extraction_result):
        """Test document processing with retry logic."""
        # First call fails, second succeeds
        service.primary_adapter.extract = AsyncMock(
            side_effect=[
                ExtractionError("Temporary failure"),
                (mock_extraction_result, 0.85, {"provider": "gemini_langextract"})
            ]
        )
        
        result = await service.process_document(sample_text, entity_id=123)
        
        assert result.primary_result == mock_extraction_result
        assert service.primary_adapter.extract.call_count == 2

    def test_create_adapter_gemini(self, service):
        """Test creating Gemini adapter."""
        from workers_py.adapters.gemini_langextract import GeminiLangExtractAdapter
        
        adapter = service._create_adapter("gemini_langextract")
        assert isinstance(adapter, GeminiLangExtractAdapter)

    def test_create_adapter_stub(self, service):
        """Test creating stub adapter."""
        from workers_py.adapters.stub import StubExtractionAdapter
        
        adapter = service._create_adapter("stub")
        assert isinstance(adapter, StubExtractionAdapter)

    def test_create_adapter_unknown(self, service):
        """Test creating unknown adapter raises error."""
        with pytest.raises(ValueError, match="Unknown extraction provider"):
            service._create_adapter("unknown_provider")


class TestExtractedDocument:
    """Test suite for ExtractedDocument container."""

    def test_extracted_document_creation(self):
        """Test ExtractedDocument creation."""
        result = ExtractionResult(
            supplier=SupplierInfo(name="Test Company"),
            invoice=InvoiceInfo(
                number="123",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00"
            ),
            lines=[]
        )
        
        doc = ExtractedDocument(
            primary_result=result,
            primary_confidence=0.85,
            primary_metadata={"provider": "test"},
            text_length=100,
            processing_time=1.5
        )
        
        assert doc.primary_result == result
        assert doc.primary_confidence == 0.85
        assert doc.primary_metadata["provider"] == "test"
        assert doc.text_length == 100
        assert doc.processing_time == 1.5
        assert doc.shadow_result is None
        assert doc.shadow_confidence is None
        assert doc.shadow_metadata is None

    def test_extracted_document_with_shadow(self):
        """Test ExtractedDocument with shadow results."""
        primary_result = ExtractionResult(
            supplier=SupplierInfo(name="Primary Company"),
            invoice=InvoiceInfo(
                number="123",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00"
            ),
            lines=[]
        )
        shadow_result = ExtractionResult(
            supplier=SupplierInfo(name="Shadow Company"),
            invoice=InvoiceInfo(
                number="456",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00"
            ),
            lines=[]
        )
        
        doc = ExtractedDocument(
            primary_result=primary_result,
            primary_confidence=0.85,
            primary_metadata={"provider": "gemini"},
            shadow_result=shadow_result,
            shadow_confidence=0.75,
            shadow_metadata={"provider": "stub"},
            text_length=100,
            processing_time=1.5
        )
        
        assert doc.primary_result == primary_result
        assert doc.shadow_result == shadow_result
        assert doc.primary_confidence == 0.85
        assert doc.shadow_confidence == 0.75
