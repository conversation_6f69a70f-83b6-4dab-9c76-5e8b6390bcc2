"""Tests for enhanced extraction service."""

import pytest
from unittest.mock import Mock, patch, AsyncMock
import os

from workers_py.services.extract_v2 import (
    EnhancedExtractionService, ExtractedDocument, create_extraction_service
)
from workers_py.adapters.base import ExtractionError
from workers_py.models import ExtractionResult, SupplierInfo, InvoiceInfo, LineItem, VatRate


class TestEnhancedExtractionService:
    """Test suite for EnhancedExtractionService."""

    @pytest.fixture
    def service(self):
        """Create service instance."""
        with patch.dict(os.environ, {
            'EXTRACTION_PROVIDER': 'stub',
            'AI_EXTRACT_RUN_MODE': 'shadow'
        }):
            return EnhancedExtractionService(entity_id=123)

    @pytest.fixture
    def sample_text(self):
        """Sample document text."""
        return "INVOICE INV-2024-001\nTest Company BVBA\nAmount: 121.00 EUR"

    @pytest.fixture
    def mock_extraction_result(self):
        """Mock extraction result."""
        return ExtractionResult(
            supplier=SupplierInfo(name="Test Company BVBA"),
            invoice=InvoiceInfo(
                number="INV-2024-001",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00"
            ),
            lines=[
                LineItem(
                    description="Test service",
                    quantity="1",
                    unit_price="100.00",
                    vat_rate=VatRate.STANDARD
                )
            ]
        )

    def test_initialization_stub_provider(self):
        """Test service initialization with stub provider."""
        with patch.dict(os.environ, {
            'EXTRACTION_PROVIDER': 'stub',
            'AI_EXTRACT_RUN_MODE': 'replace'
        }):
            service = EnhancedExtractionService(123)
            
            assert service.entity_id == 123
            assert service.provider == 'stub'
            assert service.run_mode == 'replace'
            assert service.primary_adapter is not None
            assert service.shadow_adapter is None  # No shadow for stub provider

    def test_initialization_gemini_provider(self):
        """Test service initialization with Gemini provider.""" 
        with patch.dict(os.environ, {
            'EXTRACTION_PROVIDER': 'gemini_langextract',
            'AI_EXTRACT_RUN_MODE': 'shadow'
        }):
            service = EnhancedExtractionService(123)
            
            assert service.provider == 'gemini_langextract'
            assert service.run_mode == 'shadow'
            assert service.primary_adapter is not None
            assert service.shadow_adapter is not None  # Should have stub shadow

    def test_unknown_provider_raises_error(self):
        """Test that unknown provider raises ValueError."""
        with patch.dict(os.environ, {'EXTRACTION_PROVIDER': 'unknown'}):
            with pytest.raises(ValueError, match="Unknown extraction provider"):
                EnhancedExtractionService(123)

    async def test_process_document_success(self, service, sample_text, mock_extraction_result):
        """Test successful document processing."""
        with patch.object(service, '_extract_with_retry', new_callable=AsyncMock) as mock_extract:
            mock_extract.return_value = (mock_extraction_result, 0.95, {"provider": "stub"})
            
            result = await service.process_document(sample_text, 123)
            
            assert isinstance(result, ExtractedDocument)
            assert result.primary_result == mock_extraction_result
            assert result.primary_confidence == 0.95
            assert result.text_length == len(sample_text)
            assert result.processing_time > 0

    async def test_process_empty_text_raises_error(self, service):
        """Test that empty text raises ExtractionError."""
        with pytest.raises(ExtractionError, match="Empty text provided"):
            await service.process_document("", 123)

    async def test_shadow_mode_processing(self, sample_text, mock_extraction_result):
        """Test shadow mode with both primary and shadow results."""
        with patch.dict(os.environ, {
            'EXTRACTION_PROVIDER': 'gemini_langextract',
            'AI_EXTRACT_RUN_MODE': 'shadow'
        }):
            service = EnhancedExtractionService(123)
            
            primary_result = (mock_extraction_result, 0.9, {"provider": "gemini"})
            shadow_result = (mock_extraction_result, 0.8, {"provider": "stub"})
            
            with patch.object(service, '_extract_with_retry', new_callable=AsyncMock) as mock_extract:
                mock_extract.side_effect = [primary_result, shadow_result]
                
                result = await service.process_document(sample_text, 123)
                
                assert result.primary_result == mock_extraction_result
                assert result.primary_confidence == 0.9
                assert result.shadow_result == mock_extraction_result
                assert result.shadow_confidence == 0.8
                assert mock_extract.call_count == 2

    async def test_shadow_mode_failure_continues(self, sample_text, mock_extraction_result):
        """Test that shadow extraction failure doesn't break primary processing."""
        with patch.dict(os.environ, {
            'EXTRACTION_PROVIDER': 'gemini_langextract', 
            'AI_EXTRACT_RUN_MODE': 'shadow'
        }):
            service = EnhancedExtractionService(123)
            
            primary_result = (mock_extraction_result, 0.9, {"provider": "gemini"})
            
            with patch.object(service, '_extract_with_retry', new_callable=AsyncMock) as mock_extract:
                # Primary succeeds, shadow fails
                mock_extract.side_effect = [primary_result, Exception("Shadow failed")]
                
                result = await service.process_document(sample_text, 123)
                
                assert result.primary_result == mock_extraction_result
                assert result.primary_confidence == 0.9
                assert result.shadow_result is None
                assert result.shadow_confidence is None

    async def test_extract_with_retry_success(self, service, sample_text, mock_extraction_result):
        """Test retry mechanism with successful extraction."""
        mock_adapter = Mock()
        mock_adapter.extract = AsyncMock(return_value=(mock_extraction_result, 0.9, {}))
        
        result = await service._extract_with_retry(mock_adapter, sample_text)
        
        assert result == (mock_extraction_result, 0.9, {})
        mock_adapter.extract.assert_called_once_with(sample_text)

    async def test_extract_with_retry_timeout(self, service, sample_text):
        """Test retry mechanism with timeout."""
        import asyncio
        
        mock_adapter = Mock()
        mock_adapter.extract = AsyncMock(side_effect=asyncio.TimeoutError())
        
        with patch.dict(os.environ, {'AI_EXTRACTION_RETRIES': '1'}):
            with pytest.raises(ExtractionError, match="failed after 2 attempts"):
                await service._extract_with_retry(mock_adapter, sample_text)

    async def test_extract_with_retry_exception(self, service, sample_text):
        """Test retry mechanism with regular exception."""
        mock_adapter = Mock()
        mock_adapter.extract = AsyncMock(side_effect=Exception("API error"))
        
        with patch.dict(os.environ, {'AI_EXTRACTION_RETRIES': '1'}):
            with pytest.raises(ExtractionError, match="failed after 2 attempts"):
                await service._extract_with_retry(mock_adapter, sample_text)

    def test_enhance_with_templates_no_vat(self, service, mock_extraction_result):
        """Test template enhancement when no VAT number available."""
        result = service._enhance_with_templates(mock_extraction_result, {"BE123": {"account": 6000}})
        
        # Should return unchanged result since no VAT to match
        assert result == mock_extraction_result

    def test_enhance_with_templates_matching_vat(self, service, mock_extraction_result):
        """Test template enhancement with matching VAT."""
        templates = {
            "BE0123456789": {"default_account_id": 6100}  # This should match but supplier has no VAT in fixture
        }
        
        # Set VAT on supplier
        mock_extraction_result.supplier.vat = "BE0123456789"
        
        result = service._enhance_with_templates(mock_extraction_result, templates)
        
        # Should enhance the result (implementation depends on model structure)
        assert result is not None

    def test_should_auto_suggest(self, service):
        """Test auto-suggestion confidence threshold."""
        with patch.dict(os.environ, {'AI_CONFIDENCE_THRESHOLD': '0.7'}):
            service = EnhancedExtractionService(123)
            
            assert service.should_auto_suggest(0.8) is True
            assert service.should_auto_suggest(0.6) is False
            assert service.should_auto_suggest(0.7) is True

    def test_get_extraction_summary(self, service, mock_extraction_result):
        """Test extraction summary generation."""
        doc = ExtractedDocument(
            primary_result=mock_extraction_result,
            primary_confidence=0.9,
            primary_metadata={"provider": "stub"},
            text_length=100,
            processing_time=1.5
        )
        
        summary = service.get_extraction_summary(doc)
        
        assert summary["entity_id"] == 123
        assert summary["provider"] == service.provider
        assert summary["processing_time"] == 1.5
        assert summary["primary"]["confidence"] == 0.9
        assert summary["primary"]["has_supplier"] is True
        assert summary["primary"]["has_invoice"] is True
        assert summary["primary"]["line_count"] == 1

    def test_get_extraction_summary_with_shadow(self, service, mock_extraction_result):
        """Test extraction summary with shadow result."""
        doc = ExtractedDocument(
            primary_result=mock_extraction_result,
            primary_confidence=0.9,
            primary_metadata={"provider": "gemini"},
            shadow_result=mock_extraction_result,
            shadow_confidence=0.8,
            shadow_metadata={"provider": "stub"}
        )
        
        summary = service.get_extraction_summary(doc)
        
        assert "shadow" in summary
        assert summary["shadow"]["confidence"] == 0.8
        assert summary["shadow"]["provider"] == "stub"


class TestCreateExtractionService:
    """Test factory function."""

    def test_create_extraction_service(self):
        """Test factory creates service correctly."""
        with patch.dict(os.environ, {'EXTRACTION_PROVIDER': 'stub'}):
            service = create_extraction_service(456)
            
            assert isinstance(service, EnhancedExtractionService)
            assert service.entity_id == 456


class TestExtractedDocument:
    """Test ExtractedDocument container."""

    def test_initialization(self):
        """Test ExtractedDocument initialization."""
        result = Mock(spec=ExtractionResult)
        
        doc = ExtractedDocument(
            primary_result=result,
            primary_confidence=0.9,
            primary_metadata={"provider": "test"},
            text_length=100,
            processing_time=1.5
        )
        
        assert doc.primary_result == result
        assert doc.primary_confidence == 0.9
        assert doc.primary_metadata["provider"] == "test"
        assert doc.shadow_result is None
        assert doc.text_length == 100
        assert doc.processing_time == 1.5

    def test_with_shadow_data(self):
        """Test ExtractedDocument with shadow data."""
        primary_result = Mock(spec=ExtractionResult)
        shadow_result = Mock(spec=ExtractionResult)
        
        doc = ExtractedDocument(
            primary_result=primary_result,
            primary_confidence=0.9,
            primary_metadata={"provider": "gemini"},
            shadow_result=shadow_result,
            shadow_confidence=0.8,
            shadow_metadata={"provider": "stub"}
        )
        
        assert doc.shadow_result == shadow_result
        assert doc.shadow_confidence == 0.8
        assert doc.shadow_metadata["provider"] == "stub"