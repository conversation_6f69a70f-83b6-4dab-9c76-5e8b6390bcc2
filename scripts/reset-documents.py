#!/usr/bin/env python3

"""
Document Processing Reset Script

This script resets all documents to "unprocessed" state and cleans extracted data
from the database. It can be used for both local development and staging environments.

Usage:
    python scripts/reset-documents.py                    # Reset local database
    python scripts/reset-documents.py --staging          # Reset staging database
    python scripts/reset-documents.py --prod             # Reset production database (requires confirmation)

What it does:
1. Resets inbox_documents status to 'uploaded' (unprocessed)
2. Clears all extracted data (invoices, journals, journal_lines)
3. Cleans processing results and task status
4. Clears file processing cache
5. Removes document embeddings
6. Preserves original document files and metadata
"""

import argparse
import os
import sys
from datetime import datetime
from typing import Dict, List, Tuple

import psycopg2
from psycopg2.extras import RealDictCursor


class DocumentReset:
    def __init__(self, environment: str = 'local'):
        self.environment = environment
        self.connection = None
        self.total_affected = 0
        
        # Environment configurations
        self.environments = {
            'local': {
                'host': '127.0.0.1',
                'port': 54322,
                'database': 'postgres',
                'user': 'postgres',
                'password': 'postgres'
            },
            'staging': {
                'url': os.getenv('STAGING_DATABASE_URL') or os.getenv('DATABASE_URL')
            },
            'prod': {
                'url': os.getenv('PRODUCTION_DATABASE_URL') or os.getenv('PROD_DATABASE_URL')
            }
        }

    def connect(self):
        """Establish database connection."""
        env_config = self.environments[self.environment]
        
        try:
            if 'url' in env_config and env_config['url']:
                # Use connection URL for staging/prod
                self.connection = psycopg2.connect(
                    env_config['url'],
                    cursor_factory=RealDictCursor
                )
            else:
                # Use individual parameters for local
                self.connection = psycopg2.connect(
                    host=env_config['host'],
                    port=env_config['port'],
                    database=env_config['database'],
                    user=env_config['user'],
                    password=env_config['password'],
                    cursor_factory=RealDictCursor
                )
            
            self.connection.autocommit = True
            print(f"✅ Connected to {self.environment} database")
            
        except Exception as e:
            print(f"❌ Failed to connect to {self.environment} database: {e}")
            sys.exit(1)

    def execute_query(self, query: str, description: str) -> int:
        """Execute a query and return the number of affected rows."""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query)
                affected = cursor.rowcount
                print(f"   ✅ {affected} rows affected")
                return affected

        except Exception as e:
            error_msg = str(e)
            if "does not exist" in error_msg:
                print(f"   ⚠️  Skipped: {error_msg.split(':')[1].strip()}")
            else:
                print(f"   ❌ Failed: {e}")
            return 0

    def get_reset_operations(self) -> List[Tuple[str, str]]:
        """Get list of reset operations to perform."""
        # Check which columns exist in inbox_documents
        inbox_columns = self.get_table_columns('inbox_documents')

        # Build UPDATE query based on available columns
        update_fields = ["status = 'uploaded'"]
        if 'extraction' in inbox_columns:
            update_fields.append("extraction = NULL")
        if 'confidence' in inbox_columns:
            update_fields.append("confidence = NULL")
        if 'suggestion' in inbox_columns:
            update_fields.append("suggestion = NULL")
        if 'posted_journal_id' in inbox_columns:
            update_fields.append("posted_journal_id = NULL")
        if 'export_ref' in inbox_columns:
            update_fields.append("export_ref = NULL")
        if 'error_msg' in inbox_columns:
            update_fields.append("error_msg = NULL")
        if 'confirmed_at' in inbox_columns:
            update_fields.append("confirmed_at = NULL")
        if 'confirmed_by' in inbox_columns:
            update_fields.append("confirmed_by = NULL")
        if 'updated_at' in inbox_columns:
            update_fields.append("updated_at = NOW()")

        inbox_update_query = f"""
            UPDATE inbox_documents
            SET {', '.join(update_fields)}
            WHERE status != 'uploaded'
        """

        return [
            (
                "Reset inbox documents to uploaded status",
                inbox_update_query
            ),
            (
                "Clear document processing results",
                "DELETE FROM document_processing_results"
            ),
            (
                "Clear task status",
                "DELETE FROM task_status"
            ),
            (
                "Clear file processing cache",
                "DELETE FROM file_processing_cache"
            ),
            (
                "Clear invoice embeddings",
                "DELETE FROM invoice_embeddings"
            ),
            (
                "Clear journal lines",
                "DELETE FROM journal_lines"
            ),
            (
                "Clear journals",
                "DELETE FROM journals"
            ),
            (
                "Clear invoices",
                "DELETE FROM invoices"
            ),
            (
                "Reset documents table associations",
                """
                UPDATE documents 
                SET 
                    invoice_id = NULL,
                    journal_id = NULL,
                    metadata = COALESCE(metadata, '{}')
                WHERE invoice_id IS NOT NULL OR journal_id IS NOT NULL
                """
            )
        ]

    def get_table_columns(self, table_name: str) -> List[str]:
        """Get list of columns for a table."""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = %s AND table_schema = 'public'
                """, (table_name,))
                return [row['column_name'] for row in cursor.fetchall()]
        except Exception:
            return []

    def confirm_production_reset(self) -> bool:
        """Get user confirmation for production reset."""
        print("\n⚠️  WARNING: You are about to reset PRODUCTION data!")
        print("This will permanently delete all extracted data.")
        print("Type 'CONFIRM PRODUCTION RESET' to continue:")

        confirmation = input("> ").strip()
        return confirmation == "CONFIRM PRODUCTION RESET"

    def get_table_counts(self) -> Dict[str, int]:
        """Get current row counts for relevant tables."""
        tables = [
            'inbox_documents',
            'document_processing_results', 
            'task_status',
            'file_processing_cache',
            'invoice_embeddings',
            'journal_lines',
            'journals',
            'invoices',
            'documents'
        ]
        
        counts = {}
        for table in tables:
            try:
                with self.connection.cursor() as cursor:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    result = cursor.fetchone()
                    counts[table] = result['count'] if result else 0
            except Exception:
                counts[table] = 0
                
        return counts

    def reset(self):
        """Perform the document reset."""
        print(f"🔄 Starting document reset for {self.environment} environment...")
        
        # Production safety check
        if self.environment == 'prod':
            if not self.confirm_production_reset():
                print("❌ Production reset cancelled")
                return
        
        # Connect to database
        self.connect()
        
        # Show current state
        print("\n📊 Current database state:")
        counts_before = self.get_table_counts()
        for table, count in counts_before.items():
            if count > 0:
                print(f"   {table}: {count} rows")
        
        # Perform reset operations
        print(f"\n🧹 Performing reset operations...")
        operations = self.get_reset_operations()
        results = []
        
        for description, query in operations:
            print(f"📝 {description}...")
            affected = self.execute_query(query, description)
            self.total_affected += affected
            results.append((description, affected))
        
        # Show final state
        print("\n📊 Final database state:")
        counts_after = self.get_table_counts()
        for table, count in counts_after.items():
            if count > 0:
                print(f"   {table}: {count} rows")
        
        # Summary
        print('\n📋 Reset Summary:')
        print('=' * 60)
        
        for description, affected in results:
            print(f"✅ {description}: {affected} rows")
        
        print('=' * 60)
        print(f"🎉 Reset complete! Total rows affected: {self.total_affected}")
        print(f"📅 Reset performed at: {datetime.now().isoformat()}")
        
        if self.environment == 'local':
            print('\n💡 Your local database is now clean and ready for testing!')
            print('   You can upload documents through the UI to test processing.')
        
        # Close connection
        if self.connection:
            self.connection.close()


def main():
    parser = argparse.ArgumentParser(
        description='Reset document processing data',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/reset-documents.py              # Reset local database
  python scripts/reset-documents.py --staging    # Reset staging database
  python scripts/reset-documents.py --prod       # Reset production database

What it does:
• Resets inbox_documents status to 'uploaded' (unprocessed)
• Clears all extracted data (invoices, journals, journal_lines)
• Cleans processing results and task status
• Clears file processing cache and embeddings
• Preserves original document files and metadata
        """
    )
    
    parser.add_argument(
        '--staging',
        action='store_true',
        help='Reset staging environment'
    )
    
    parser.add_argument(
        '--prod', '--production',
        action='store_true',
        help='Reset production environment (requires confirmation)'
    )
    
    args = parser.parse_args()
    
    # Determine environment
    if args.prod:
        environment = 'prod'
    elif args.staging:
        environment = 'staging'
    else:
        environment = 'local'
    
    # Perform reset
    try:
        reset_tool = DocumentReset(environment)
        reset_tool.reset()
        print('\n✨ All done!')
        
    except KeyboardInterrupt:
        print('\n❌ Reset cancelled by user')
        sys.exit(1)
    except Exception as e:
        print(f'\n💥 Reset failed: {e}')
        sys.exit(1)


if __name__ == '__main__':
    main()
